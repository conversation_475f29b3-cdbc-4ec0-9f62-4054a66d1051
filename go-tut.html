<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Go Learning Guide</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chosen Palette: Warm Neutrals -->
    <!-- Application Structure Plan: The application is structured into three main thematic sections: Beginner, Intermediate, and Advanced, mirroring the progressive learning path of the source document. This structure was chosen for its intuitive user flow, allowing learners to navigate based on their skill level. The core interaction is a quiz-based format within each topic, where users can attempt questions and then reveal solutions. A dynamic progress chart provides immediate visual feedback, enhancing engagement and motivation. Navigation is handled by a persistent sidebar, allowing easy switching between levels and topics. -->
    <!-- Visualization & Content Choices: The primary visualization is a dynamic donut chart (Chart.js/Canvas) used as a progress tracker for each topic's quiz. Goal: Inform/Motivate. Interaction: The chart updates automatically as the user answers questions. Justification: This provides a clear, immediate, and engaging visual representation of the user's progress, which is more effective than a simple text counter. Code solutions are presented in structured HTML with Tailwind for clarity and readability. Goal: Inform/Educate. Interaction: Users click a button to reveal the solution. Justification: This encourages active recall and self-assessment before viewing the answer. All content is organized into expandable sections to keep the UI clean and focused. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .solution-code {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', Courier, monospace;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="flex h-screen">
        <!-- Sidebar Navigation -->
        <aside class="w-64 bg-white shadow-md p-6 hidden md:block">
            <h1 class="text-2xl font-bold text-cyan-700 mb-8">Go Mastery Guide</h1>
            <nav id="sidebar-nav">
                <!-- Navigation links will be dynamically inserted here -->
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 md:p-10 overflow-y-auto">
            <div id="app-content">
                <!-- Content will be dynamically rendered here -->
            </div>
        </main>
    </div>

    <script>
        const contentData = {
            beginner: {
                title: '🟢 Beginner (Fundamentals)',
                topics: {
                    introduction: {
                        title: 'Introduction to Go',
                        description: "Go, also known as Golang, is an open-source, statically-typed, and compiled programming language designed at Google. It was created to address the challenges of modern software development, such as concurrency, distributed systems, and massive codebases. Its simple syntax and powerful standard library make it a favorite for building fast and reliable server-side applications, command-line tools, and web services.",
                        questions: [
                            { q: "What is the primary motivation behind Go's creation, and how does it differ from a language like Python?", s: "// Go was created to simplify the development of concurrent and distributed systems. Unlike Python, which is an interpreted language, Go is a compiled language, which generally results in better performance. Go also has built-in concurrency features like goroutines and channels, which are not as natively integrated into Python's core language." },
                            { q: "Write the command to build a Go program named `main.go` into an executable file.", s: "// To build a Go program, you use the `go build` command.\n\ngo build main.go\n\n// This will create an executable file named `main` (or `main.exe` on Windows) in the current directory." },
                            { q: "Explain the purpose of the `go mod init` and `go mod tidy` commands.", s: "// `go mod init <module_name>` initializes a new Go module in the current directory. This creates a `go.mod` file that tracks the module's dependencies.\n\n// `go mod tidy` cleans up the `go.mod` file by adding any missing dependencies and removing unused ones. It ensures your module's dependency graph is consistent." }
                        ]
                    },
                    syntax: {
                        title: 'Basic Syntax',
                        description: "Go's syntax is intentionally simple and minimal. It's designed to be easy to read and write. `var` is used for explicit variable declarations, while `:=` is the short declaration operator, commonly used inside functions. `const` is for constants. Go has a rich set of built-in data types, including `int`, `float64`, `string`, and `bool`.",
                        questions: [
                            { q: "Declare an integer variable named `age` and a string variable named `name` using `var` and initialize them with values.", s: 'package main\n\nimport "fmt"\n\nfunc main() {\n\tvar age int = 30\n\tvar name string = "Alice"\n\n\tfmt.Println("Name:", name)\n\tfmt.Println("Age:", age)\n}' },
                            { q: "Declare a constant for the value of pi using `const` and an integer constant using `iota` that starts at 10.", s: 'package main\n\nimport "fmt"\n\nfunc main() {\n\tconst Pi = 3.14159\n\n\tconst (\n\t\tA = iota + 10 // A will be 10\n\t\tB             // B will be 11\n\t\tC             // C will be 12\n\t)\n\n\tfmt.Println("Pi:", Pi)\n\tfmt.Println("A:", A, "B:", B, "C:", C)\n}' },
                            { q: "Perform a type conversion to convert a `float64` variable to an `int` and print the result.", s: 'package main\n\nimport "fmt"\n\nfunc main() {\n\tvar myFloat float64 = 99.99\n\tvar myInt int = int(myFloat)\n\n\tfmt.Println("Original float:", myFloat)\n\tfmt.Println("Converted integer:", myInt)\n}' }
                        ]
                    },
                    structs: {
                        title: 'Structs',
                        description: "A `struct` is a composite data type that groups together fields of different data types. They are used to create custom data structures. Struct methods are functions associated with a specific struct type, and they use a receiver to bind the method to an instance of the struct.",
                        questions: [
                            { q: "Define a `Person` struct with fields for `name` (string) and `age` (int). Create an instance of this struct and print its fields.", s: 'package main\n\nimport "fmt"\n\ntype Person struct {\n\tname string\n\tage  int\n}\n\nfunc main() {\n\tp := Person{name: "Alice", age: 30}\n\tfmt.Printf("Name: %s, Age: %d\\n", p.name, p.age)\n}'},
                            { q: "Add a method to the `Person` struct called `Greet` that prints a greeting including the person's name.", s: 'package main\n\nimport "fmt"\n\ntype Person struct {\n\tname string\n\tage  int\n}\n\nfunc (p Person) Greet() {\n\tfmt.Printf("Hello, my name is %s.\\n", p.name)\n}\n\nfunc main() {\n\tp := Person{name: "Bob", age: 25}\n\tp.Greet()\n}'},
                            { q: "Write a method `Birthday` with a pointer receiver that increments the person's age.", s: 'package main\n\nimport "fmt"\n\ntype Person struct {\n\tname string\n\tage  int\n}\n\nfunc (p *Person) Birthday() {\n\tp.age++\n}\n\nfunc main() {\n\tp := Person{name: "Charlie", age: 40}\n\tfmt.Println("Before Birthday:", p.age)\n\tp.Birthday()\n\tfmt.Println("After Birthday:", p.age)\n}'}
                        ]
                    }
                }
            },
            intermediate: {
                title: '🟡 Intermediate (Building Blocks)',
                topics: {
                    packages: {
                        title: 'Modules and Packages',
                        description: "Go code is organized into packages. A package is a collection of source files in the same directory that are compiled together. A module is a collection of related packages. Go's visibility rules are simple: identifiers that start with an uppercase letter are `exported` (public), while those that start with a lowercase letter are `unexported` (private).",
                        questions: [
                            { q: "Create a module named `mymath` and a package within it that contains a function `Add` to add two integers.", s: "// First, create a new directory for your module and a subdirectory for the package.\n//\n// $ mkdir mymath\n// $ cd mymath\n// $ go mod init mymath\n//\n// Then, create a file named `math.go` inside the `mymath` directory.\n//\n// mymath/math.go\npackage mymath\n\n// Add is an exported function because it starts with a capital letter.\nfunc Add(x, y int) int {\n\treturn x + y\n}" },
                            { q: "In a separate `main` package, import your `mymath` package and use the `Add` function.", s: "// In a separate directory, create a new main module.\n//\n// $ mkdir myapp\n// $ cd myapp\n// $ go mod init myapp\n//\n// Then, create a `main.go` file. You also need to reference your local module.\n// In the go.mod file, add a `replace` directive:\n//\n// require mymath v0.0.0\n// replace mymath => ../mymath\n//\n// Then run `go mod tidy`\n//\n// main.go\npackage main\n\nimport (\n\t\"fmt\"\n\t\"myapp/mymath\" // The package path from go.mod\n)\n\nfunc main() {\n\tsum := mymath.Add(10, 5)\n\tfmt.Println(\"The sum is:\", sum)\n}"},
                            { q: "Explain the difference between a `go.mod` file and a `go.sum` file.", s: "// A go.mod file defines a module's identity and its direct dependencies. It is a record of a project's dependencies and their versions.\n\n// A go.sum file is a cryptographic checksum of the contents of the module's dependencies. It is used to verify the integrity of downloaded modules, ensuring that the same module version is not modified over time." }
                        ]
                    },
                    concurrency: {
                        title: "Concurrency (Go's Superpower)",
                        description: "Go's approach to concurrency is based on communicating sequential processes (CSP), using `goroutines` and `channels`. A `goroutine` is a lightweight thread of execution, and a `channel` is a communication mechanism that allows goroutines to exchange data.",
                        questions: [
                            { q: "Write a program that launches a goroutine to print \"Hello from a goroutine!\" and then the main function prints \"Hello from main!\".", s: 'package main\n\nimport (\n\t"fmt"\n\t"time"\n)\n\nfunc sayHello() {\n\tfmt.Println("Hello from a goroutine!")\n}\n\nfunc main() {\n\tgo sayHello()\n\tfmt.Println("Hello from main!")\n\t// Give the goroutine time to run\n\ttime.Sleep(1 * time.Second)\n}'},
                            { q: "Write a program where a goroutine generates numbers and sends them on a channel, and the main function receives and prints them.", s: 'package main\n\nimport "fmt"\n\nfunc generateNumbers(ch chan int) {\n\tfor i := 0; i < 5; i++ {\n\t\tch <- i // Send number to channel\n\t}\n\tclose(ch) // Close the channel when done\n}\n\nfunc main() {\n\tch := make(chan int)\n\tgo generateNumbers(ch)\n\n\tfor num := range ch { // Loop until the channel is closed\n\t\tfmt.Println("Received:", num)\n\t}\n}'},
                            { q: "Use a `select` statement to listen for data from two different channels.", s: 'package main\n\nimport (\n\t"fmt"\n\t"time"\n)\n\nfunc producer1(ch chan string) {\n\ttime.Sleep(2 * time.Second)\n\tch <- "data from producer 1"\n}\n\nfunc producer2(ch chan string) {\n\ttime.Sleep(1 * time.Second)\n\tch <- "data from producer 2"\n}\n\nfunc main() {\n\tch1 := make(chan string)\n\tch2 := make(chan string)\n\n\tgo producer1(ch1)\n\tgo producer2(ch2)\n\n\tselect {\n\tcase msg1 := <-ch1:\n\t\tfmt.Println("Received from ch1:", msg1)\n\tcase msg2 := <-ch2:\n\t\tfmt.Println("Received from ch2:", msg2)\n\t}\n}'}
                        ]
                    }
                }
            },
            advanced: {
                title: '🔴 Advanced (Mastery)',
                topics: {
                    advconcurrency: {
                        title: 'Advanced Concurrency',
                        description: "While goroutines and channels are powerful, real-world applications often require more control over concurrent access to shared resources. Go's `sync` package provides tools like `Mutexes` for mutual exclusion and `WaitGroups` for coordinating goroutines.",
                        questions: [
                            { q: "Write a program that uses a `sync.WaitGroup` to wait for 5 goroutines to complete their work before the main function exits.", s: 'package main\n\nimport (\n\t"fmt"\n\t"sync"\n\t"time"\n)\n\nfunc worker(id int, wg *sync.WaitGroup) {\n\tdefer wg.Done()\n\tfmt.Printf("Worker %d starting...\\n", id)\n\ttime.Sleep(1 * time.Second)\n\tfmt.Printf("Worker %d finished.\\n", id)\n}\n\nfunc main() {\n\tvar wg sync.WaitGroup\n\tfor i := 1; i <= 5; i++ {\n\t\twg.Add(1)\n\t\tgo worker(i, &wg)\n\t}\n\twg.Wait()\n\tfmt.Println("All workers have completed their work.")\n}'},
                            { q: "Use a `sync.Mutex` to safely increment a counter from multiple goroutines, preventing a race condition.", s: 'package main\n\nimport (\n\t"fmt"\n\t"sync"\n)\n\nfunc main() {\n\tvar mu sync.Mutex\n\tcounter := 0\n\tvar wg sync.WaitGroup\n\n\tfor i := 0; i < 1000; i++ {\n\t\twg.Add(1)\n\t\tgo func() {\n\t\t\tdefer wg.Done()\n\t\t\tmu.Lock()\n\t\t\tcounter++\n\t\t\tmu.Unlock()\n\t\t}()\n\t}\n\n\twg.Wait()\n\tfmt.Println("Final Counter:", counter)\n}'},
                            { q: "Create a simple worker pool of 3 goroutines to process 10 jobs.", s: 'package main\n\nimport (\n\t"fmt"\n)\n\nfunc workerPool(id int, jobs <-chan int, results chan<- int) {\n\tfor j := range jobs {\n\t\tfmt.Printf("Worker %d started job %d\\n", id, j)\n\t\tresults <- j * 2\n\t}\n}\n\nfunc main() {\n\tconst numJobs = 10\n\tjobs := make(chan int, numJobs)\n\tresults := make(chan int, numJobs)\n\n\tfor w := 1; w <= 3; w++ {\n\t\tgo workerPool(w, jobs, results)\n\t}\n\n\tfor j := 1; j <= numJobs; j++ {\n\t\tjobs <- j\n\t}\n\tclose(jobs)\n\n\tfor a := 1; a <= numJobs; a++ {\n\t\t<-results\n\t}\n}'}
                        ]
                    },
                    generics: {
                        title: 'Generics (Go 1.18+)',
                        description: "Generics were introduced in Go 1.18, allowing you to write functions and data structures that work with a range of types, without sacrificing type safety. This eliminates the need for the `empty interface` in many cases.",
                        questions: [
                            { q: "Write a generic function `Sum[T int | float64]` that sums the elements of a slice of any type that satisfies the constraint.", s: 'package main\n\nimport "fmt"\n\ntype Number interface {\n\tint | int8 | int16 | int32 | int64 | float32 | float64\n}\n\nfunc Sum[T Number](items []T) T {\n\tvar total T\n\tfor _, item := range items {\n\t\ttotal += item\n\t}\n\treturn total\n}\n\nfunc main() {\n\tintSlice := []int{1, 2, 3, 4, 5}\n\tfloatSlice := []float64{1.1, 2.2, 3.3}\n\n\tfmt.Println("Sum of ints:", Sum(intSlice))\n\tfmt.Println("Sum of floats:", Sum(floatSlice))\n}'},
                            { q: "Create a generic `Stack` struct that can hold elements of any type and has `Push` and `Pop` methods.", s: 'package main\n\nimport "fmt"\n\ntype Stack[T any] struct {\n\titems []T\n}\n\nfunc (s *Stack[T]) Push(item T) {\n\ts.items = append(s.items, item)\n}\n\nfunc (s *Stack[T]) Pop() (T, bool) {\n\tif len(s.items) == 0 {\n\t\tvar zero T\n\t\treturn zero, false\n\t}\n\tlastIndex := len(s.items) - 1\n\titem := s.items[lastIndex]\n\ts.items = s.items[:lastIndex]\n\treturn item, true\n}\n\nfunc main() {\n\tintStack := Stack[int]{}\n\tintStack.Push(10)\n\tintStack.Push(20)\n\titem, ok := intStack.Pop()\n\tfmt.Printf("Popped %d, success: %t\\n", item, ok)\n}'}
                        ]
                    }
                }
            }
        };

        const appContent = document.getElementById('app-content');
        const sidebarNav = document.getElementById('sidebar-nav');
        let charts = {};

        function init() {
            buildSidebar();
            renderContent('beginner', 'introduction');
        }

        function buildSidebar() {
            let navHtml = '';
            for (const levelKey in contentData) {
                const level = contentData[levelKey];
                navHtml += `<h3 class="text-lg font-semibold mt-6 mb-2 text-gray-600">${level.title}</h3>`;
                navHtml += '<ul>';
                for (const topicKey in level.topics) {
                    const topic = level.topics[topicKey];
                    navHtml += `<li><a href="#" class="block py-2 px-4 rounded hover:bg-cyan-50 transition-colors duration-200" onclick="renderContent('${levelKey}', '${topicKey}')">${topic.title}</a></li>`;
                }
                navHtml += '</ul>';
            }
            sidebarNav.innerHTML = navHtml;
        }

        function renderContent(levelKey, topicKey) {
            const topic = contentData[levelKey].topics[topicKey];
            const totalQuestions = topic.questions.length;
            let answeredQuestions = 0;

            let contentHtml = `
                <div class="bg-white p-8 rounded-lg shadow-lg">
                    <h2 class="text-3xl font-bold mb-4">${topic.title}</h2>
                    <p class="text-gray-600 mb-8">${topic.description}</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="md:col-span-2">
                            <h3 class="text-xl font-semibold mb-4">Practice Questions</h3>
                            <div id="questions-container">
                                ${topic.questions.map((q, index) => `
                                    <div class="mb-6 border border-gray-200 rounded-lg p-4">
                                        <p class="font-medium mb-2"><strong>Question ${index + 1}:</strong> ${q.q}</p>
                                        <button class="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700 transition-colors duration-200" onclick="revealSolution(this, ${index}, '${levelKey}', '${topicKey}')">Reveal Solution</button>
                                        <div class="solution mt-4 hidden">
                                            <p class="font-semibold mb-2">Solution:</p>
                                            <div class="solution-code">${q.s.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-4">Your Progress</h3>
                            <div class="chart-container relative h-64 w-full max-w-xs mx-auto">
                                <canvas id="progress-chart-${levelKey}-${topicKey}"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            appContent.innerHTML = contentHtml;
            createOrUpdateChart(levelKey, topicKey, answeredQuestions, totalQuestions);
        }
        
        window.revealSolution = function(button, questionIndex, levelKey, topicKey) {
            const solutionDiv = button.nextElementSibling;
            const isHidden = solutionDiv.classList.contains('hidden');
            
            if (isHidden) {
                solutionDiv.classList.remove('hidden');
                button.textContent = 'Hide Solution';
                button.classList.remove('bg-cyan-600');
                button.classList.add('bg-gray-500');

                const topic = contentData[levelKey].topics[topicKey];
                const totalQuestions = topic.questions.length;
                
                // This logic assumes revealing a solution counts as "answering" it.
                // A more complex implementation could track correct/incorrect answers.
                let answeredCount = 0;
                document.querySelectorAll('.solution').forEach(sol => {
                    if (!sol.classList.contains('hidden')) {
                        answeredCount++;
                    }
                });
                createOrUpdateChart(levelKey, topicKey, answeredCount, totalQuestions);

            } else {
                solutionDiv.classList.add('hidden');
                button.textContent = 'Reveal Solution';
                button.classList.remove('bg-gray-500');
                button.classList.add('bg-cyan-600');
            }
        }

        function createOrUpdateChart(levelKey, topicKey, answered, total) {
            const chartId = `progress-chart-${levelKey}-${topicKey}`;
            const ctx = document.getElementById(chartId).getContext('2d');
            const remaining = total - answered;

            if (charts[chartId]) {
                charts[chartId].data.datasets[0].data = [answered, remaining];
                charts[chartId].update();
            } else {
                charts[chartId] = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Answered', 'Remaining'],
                        datasets: [{
                            data: [answered, remaining],
                            backgroundColor: ['#0891b2', '#e5e7eb'],
                            borderColor: ['#ffffff', '#ffffff'],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: {
                            legend: {
                                position: 'bottom',
                            },
                            tooltip: {
                                enabled: true
                            }
                        }
                    }
                });
            }
        }

        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
